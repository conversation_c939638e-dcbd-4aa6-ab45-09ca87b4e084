"use client"

import Image from "next/image";
import { useEffect, useState } from "react";

export default function Home() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [showNavbar, setShowNavbar] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setIsScrolled(currentScrollY > 20);
      
      // Hide navbar on scroll down, show on scroll up
      if (currentScrollY > lastScrollY) {
        setShowNavbar(false);
      } else {
        setShowNavbar(true);
      }
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);
  
  return (
    <div className="min-h-screen bg-white">
      {/* Floating Navigation */}
      <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        showNavbar ? 'translate-y-0' : '-translate-y-full'
      }`}>
        <div className={`mx-auto my-6 max-w-[80%] transition-all duration-300 ${
          isScrolled 
            ? 'bg-white shadow-lg rounded-full border border-gray-200' 
            : 'bg-white rounded-full border border-white/20'
        }`}>
          <div className="px-8 lg:px-10">
            <div className="flex justify-between items-center py-4">
              {/* Logo */}
              <div className="flex items-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-orange-500`}>
                  <span className="text-white font-bold text-lg">T</span>
                </div>
                <h1 className="text-xl font-bold text-teal-900">
                  Trinova
                </h1>
              </div>

              {/* Navigation Menu */}
              <nav className="hidden md:flex items-center space-x-6 lg:space-x-10">
                <div className="relative group">
                  <a href="#home" className="font-medium text-gray-900 hover:text-orange-500 transition-colors flex items-center">
                    Home
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </a>
                </div>
                <div className="relative group">
                  <a href="#pages" className="font-medium text-gray-900 hover:text-orange-500 transition-colors flex items-center">
                    Pages
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </a>
                </div>
                <div className="relative group">
                  <a href="#services" className="font-medium text-gray-900 hover:text-orange-500 transition-colors flex items-center">
                    Services
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </a>
                </div>
                <div className="relative group">
                  <a href="#project" className="font-medium text-gray-900 hover:text-orange-500 transition-colors flex items-center">
                    Project
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </a>
                </div>
              </nav>

              {/* Contact Info & Menu Button */}
              <div className="flex items-center space-x-4">
                <div className="hidden lg:flex items-center space-x-2">
                  <span className="text-orange-500 text-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V6h16v12zM6 10h2v2H6zm0 4h8v2H6zm10 0h2v2h-2zm-6-4h8v2h-8z"/>
                    </svg>
                  </span>
                  <span className="text-sm font-medium text-gray-700">
                    +254702236510
                  </span>
                </div>
                <button className="w-11 h-11 rounded-full flex items-center justify-center transition-colors bg-orange-500 hover:bg-orange-600">
                  <div className="flex flex-col justify-center items-center space-y-1">
                    <div className="w-5 h-0.5 bg-white"></div>
                    <div className="w-5 h-0.5 bg-white"></div>
                    <div className="w-5 h-0.5 bg-white"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen bg-gradient-to-br from-teal-800 via-teal-700 to-emerald-800 overflow-hidden">
        {/* Decorative Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-96 h-96 bg-teal-600 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-80 h-80 bg-emerald-600 rounded-full opacity-20 blur-3xl"></div>
        </div>
        
        {/* Vertical "Scroll Down" Text */}
        <div className="absolute left-8 top-1/2 transform -translate-y-1/2 -rotate-90 text-white/60 text-sm font-medium tracking-widest">
          SCROLL DOWN
        </div>

        <div className="relative max-w-[85%] mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
            <div className="space-y-8">
              {/* Watch Story Button */}
              {/* <div className="flex items-center gap-4 mb-8">
                <div className="relative">
                  <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[8px] border-l-white border-t-[6px] border-t-transparent border-b-[6px] border-b-transparent ml-1"></div>
                    </div>
                  </div>
                </div>
                <div className="text-white">
                  <p className="font-medium">Watch</p>
                  <p className="font-medium">Trinova Story</p>
                </div>
              </div> */}

              <h1 className="text-6xl lg:text-7xl font-bold text-white leading-tight">
                Streamline Business<br />
                <span className="text-white">Operations</span><br />
                <span className="text-white">Unleash Growth</span>
              </h1>

              <button className="bg-lime-400 text-teal-900 px-8 py-4 rounded-full hover:bg-lime-300 transition-colors font-semibold text-lg inline-flex items-center gap-2">
                Let's Talk Trinova
                <span className="text-xl">→</span>
              </button>
            </div>

            <div className="relative">
              {/* Main Professional Image */}
              <div className="relative rounded-3xl overflow-hidden shadow-2xl">
                <div className="bg-gradient-to-br from-gray-100 to-gray-200 h-96 lg:h-[500px] flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <span className="text-3xl">👥</span>
                    </div>
                    <p className="text-sm">Professional Team Image</p>
                  </div>
                </div>
              </div>

              {/* Orange Stats Card */}
              {/* <div className="absolute -bottom-8 -left-8 bg-orange-500 rounded-2xl p-6 text-white shadow-xl">
                <div className="text-center">
                  <h3 className="text-4xl font-bold mb-2">5k</h3>
                  <p className="text-orange-100 text-sm font-medium">We Consultant Client<br />World-wide</p>
                </div>
              </div> */}

              {/* Additional floating elements */}
              <div className="absolute top-4 right-4 bg-white/10 backdrop-blur-sm rounded-xl p-3 text-white border border-white/20">
                <div className="flex items-center gap-2">
                  {/* <div className="flex -space-x-1">
                    <div className="w-6 h-6 bg-blue-400 rounded-full border border-white"></div>
                    <div className="w-6 h-6 bg-green-400 rounded-full border border-white"></div>
                    <div className="w-6 h-6 bg-purple-400 rounded-full border border-white"></div>
                  </div>
                  <div className="text-xs text-teal-900">
                    <p className="font-medium">Happy Customers</p>
                    <div className="flex items-center gap-1">
                      <span className="text-yellow-400 text-xs">★★★★★</span>
                      <span className="">4.8(120K)</span>
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex items-center gap-3 mb-12">
            <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">★</span>
            </div>
            <span className="text-orange-500 font-medium uppercase tracking-wider text-sm">ABOUT BENTOL</span>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-start">

            {/* Left Column - Heading and Team Image */}
            <div className="lg:col-span-5 space-y-8">
              <div>
                <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-8 leading-tight">
                  Best powerful business consulting agency for brand success
                </h2>
              </div>

              <div className="relative">
                <div className="rounded-2xl overflow-hidden shadow-lg">
                  <img
                    src="/team-meeting.jpg"
                    alt="Business consulting team collaboration"
                    className="w-full h-[320px] object-cover"
                  />
                </div>
              </div>
            </div>

            {/* Middle Column - Circular Badge and Since 2006 */}
            <div className="lg:col-span-4 flex flex-col items-center space-y-8">
              {/* Circular Badge */}
              <div className="relative flex justify-center">
                <div className="relative w-32 h-32">
                  {/* Star icon in the center */}
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <svg className="w-8 h-8 text-orange-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor" />
                    </svg>
                  </div>

                  {/* Rotating circular text */}
                  <svg className="absolute inset-0 w-full h-full animate-slow-spin" viewBox="0 0 100 100">
                    <defs>
                      <path id="circle" d="M 50, 50 m -35, 0 a 35,35 0 1,1 70,0 a 35,35 0 1,1 -70,0" />
                    </defs>
                    <text fill="#6B7280" fontSize="4.5" letterSpacing="1.5" fontWeight="600">
                      <textPath xlinkHref="#circle" startOffset="0%">
                        DEVELOPMENTS • IT • BUSINESS • CONSULTANTS •
                      </textPath>
                    </text>
                  </svg>
                </div>
              </div>

              {/* Since 2006 Section */}
              <div className="text-center max-w-sm">
                <h3 className="text-xl font-bold text-gray-900 mb-4">SINCE 2006</h3>
                <p className="text-gray-600 leading-relaxed text-sm mb-6">
                  Collaboratively <span className="text-orange-500 font-medium">disintermediate</span> one to functionalities and long term impacter compellingly granulars web leveling competencie consult
                </p>
                <div>
                  <a href="#" className="inline-flex items-center px-6 py-3 border-2 border-gray-300 rounded-full text-gray-800 text-sm font-medium hover:bg-orange-500 hover:text-white hover:border-orange-500 transition-all duration-300">
                    More About <span className="ml-2">→</span>
                  </a>
                </div>
              </div>
            </div>

            {/* Right Column - Years Experience and CEO */}
            <div className="lg:col-span-3 space-y-8">
              {/* Years of Experience */}
              <div className="text-center lg:text-left">
                <div className="relative inline-flex items-start">
                  <div className="relative">
                    <span className="text-7xl lg:text-8xl font-bold text-gray-900 leading-none flex items-center">
                      <span>18</span>
                      <span className="absolute -top-2 -right-4 h-5 w-5 bg-orange-500 rounded-full flex items-center justify-center text-xs text-white font-bold">+</span>
                    </span>
                  </div>
                  <div className="ml-3 mt-6">
                    <p className="text-gray-700 font-medium leading-tight text-base">
                      Years of<br />Pro Experiences
                    </p>
                  </div>
                </div>
              </div>

              {/* CEO Section */}
              <div className="relative">
                <div className="rounded-2xl overflow-hidden shadow-lg bg-gradient-to-br from-orange-400 to-orange-600">
                  <div className="relative h-[280px]">
                    <img
                      src="/founder.png"
                      alt="David Watson - CEO Founder"
                      className="w-full h-full object-cover object-center"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.innerHTML = `
                            <div class="flex items-center justify-center h-full bg-gradient-to-br from-orange-400 to-orange-600">
                              <div class="text-center text-white">
                                <div class="w-20 h-20 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                                  <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                                  </svg>
                                </div>
                                <h4 class="text-xl font-bold">DAVID WATSON</h4>
                                <p class="text-sm opacity-90">CEO Founder</p>
                              </div>
                            </div>
                          `;
                        }
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                    <div className="absolute bottom-6 left-6 text-white">
                      <h4 className="text-xl font-bold mb-1">DAVID WATSON</h4>
                      <p className="text-sm opacity-90">CEO Founder</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
                <div className="rounded-xl overflow-hidden shadow-lg">
                  <div className="relative bg-gradient-to-b from-orange-500 to-teal-800 h-48 flex items-end">
                    <img 
                      src="/founder.png" 
                      alt="George Osodo" 
                      className="w-full h-full object-cover object-center"
                      onError={(e) => {
                        e.currentTarget.onerror = null;
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          parent.classList.add('bg-gradient-to-b', 'from-orange-500', 'to-teal-800');
                          parent.innerHTML = `
                            <div className="absolute inset-0 flex items-center justify-center">
                              <div className="text-center text-white px-4">
                                <div className="w-20 h-20 bg-white/20 backdrop-blur-md rounded-full mx-auto mb-2 flex items-center justify-center border border-white/40">
                                  <span className="text-3xl">👤</span>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-0 left-0 right-0 p-3 text-center text-white bg-black/30 backdrop-blur-sm">
                              <h4 className="font-bold text-xl tracking-wide">GEORGE OSODO</h4>
                              <p className="text-xs text-white/80">CEO Founder</p>
                            </div>
                          `;
                        }
                      }}
                    />
                    <div className="absolute bottom-0 left-0 right-0 p-3 text-center text-white bg-black/30 backdrop-blur-sm">
                      <h4 className="font-bold text-xl tracking-wide">GEORGE OSODO</h4>
                      <p className="text-xs text-white/80">CEO Founder</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Trinova Section */}
      <section id="why-trinova" className="py-20 bg-gradient-to-r from-slate-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left Column - Team Image */}
            <div className="order-2 lg:order-1">
              <div className="relative">
                <div className="rounded-xl overflow-hidden">
                  <img 
                    src="/team-meeting.jpg" 
                    alt="Professional team meeting" 
                    className="w-full h-[500px] object-cover"
                    onError={(e) => {
                      e.currentTarget.onerror = null;
                      e.currentTarget.style.display = 'none';
                      const parent = e.currentTarget.parentElement;
                      if (parent) {
                        parent.classList.add('bg-gray-200');
                        parent.innerHTML = `                        <div className="p-6 flex items-center justify-center h-[500px]">
                          <div className="text-center">
                            <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                              <span className="text-4xl">👥</span>
                            </div>
                            <p className="text-gray-500">Professional Team Image</p>
                          </div>
                        </div>
                        `;
                      }
                    }}
                  />
                </div>
                
                {/* Since 2023 Circle */}
                <div className="absolute bottom-10 right-5 lg:bottom-10 lg:right-10">
                  <div className="relative w-40 h-40 bg-lime-100 rounded-full flex flex-col items-center justify-center shadow-lg">
                    <div className="absolute w-36 h-36 bg-gradient-to-br from-teal-800 to-gray-800 rounded-full inset-0 m-auto z-0"></div>
                    <div className="absolute inset-0 flex flex-col items-center justify-center z-10 text-white">
                      <div className="text-xl font-bold">SINCE 2023</div>
                      <div className="w-10 h-0.5 bg-gray-400 my-1"></div>
                      <div className="text-xs text-center px-2 font-light">
                        Providing digital solutions
                        <br/>client satisfaction
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Right Column - Why Trinova Content */}
            <div className="order-1 lg:order-2">  
              <div className="mb-6 flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">★</span>
                </div>
                <span className="text-orange-500 font-bold uppercase tracking-wider">WHY TRINOVA</span>
              </div>           
              <h2 className="text-5xl font-bold text-teal-900 mb-6 leading-tight">
                We make the most creative <br />
                <span className="text-teal-900">digital</span> solutions
              </h2>
              
              <p className="text-gray-600 mb-12 text-lg leading-relaxed">
                Collaboratively supply bricks-and-clicks metrics for maintainable users reinvent 
                unique value for just in time consult.
              </p>
              
              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-10">
                <div className="flex items-start">
                  <span className="text-orange-500 mr-3 text-xl">✓</span>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Most powerful and<br />expert team</h3>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-orange-500 mr-3 text-xl">✓</span>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Data-driven insights for<br />consulting</h3>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-orange-500 mr-3 text-xl">✓</span>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Innovative strategies<br />for business</h3>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="text-orange-500 mr-3 text-xl">✓</span>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Client-centric approach<br />to marketing</h3>
                  </div>
                </div>
              </div>
              
              {/* Meet Our Team Button */}
              <div className="mt-12">
                <a 
                  href="#team" 
                  className="inline-flex items-center px-8 py-4 bg-teal-900 text-white rounded-lg font-semibold hover:bg-teal-800 transition-colors"
                >
                  Meet Our Team
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-teal-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center gap-3 mb-6 justify-center">
              <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">★</span>
              </div>
              <span className="text-orange-500 font-medium uppercase tracking-wider text-sm">OUR SOLUTIONS</span>
            </div>
            <h2 className="text-4xl font-bold text-white mt-4 mb-6">
              Providing the best <span className="text-lime-400">solutions</span><br />
              for your business
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mt-24">
            {/* Service 1 - KadereConnect */}
            <div className="text-white">
              <h3 className="text-5xl font-bold mb-4">01.</h3>
              <h4 className="text-2xl font-bold mb-4">KadereConnect</h4>
              <p className="text-gray-300 mb-6">
                KadereConnect is our powerful, cloud-native SaaS platform that streamlines fleet management, driver onboarding, and vehicle document compliance for Kenyan businesses.
              </p>
            </div>

            {/* Service 2 - Custom Development */}
            <div className="text-white">
              <h3 className="text-5xl font-bold mb-4">02.</h3>
              <h4 className="text-2xl font-bold mb-4">Custom Development</h4>
              <p className="text-gray-300 mb-6">
                We offer tailored development services to help businesses build scalable, secure digital solutions that meet their unique needs.
              </p>
            </div>

            {/* Service 3 - Digital Transformation */}
            <div className="text-white">
              <h3 className="text-5xl font-bold mb-4">03.</h3>
              <h4 className="text-2xl font-bold mb-4">Digital Transformation</h4>
              <p className="text-gray-300 mb-6">
                We partner with you to transform your business processes, integrating digital solutions to enhance operational efficiency and future growth.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-24 bg-white relative">
        <div className="absolute top-0 left-0 w-1/4 h-full bg-gray-50 -z-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
            {/* Left Side - Contact Form */}
            <div className="bg-white rounded-2xl shadow-xl p-8 lg:p-10">
              <h2 className="text-3xl font-bold text-teal-900 mb-2">Make an Appointment</h2>
              <p className="text-gray-500 mb-8">(24/7 available)</p>
              
              <form className="space-y-5">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none text-orange-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <input 
                      type="text" 
                      placeholder="Your Name" 
                      className="w-full pl-14 pr-4 py-3.5 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                    />
                  </div>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none text-orange-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                    </div>
                    <input 
                      type="email" 
                      placeholder="Email Address" 
                      className="w-full pl-14 pr-4 py-3.5 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div className="relative">
                  <select className="w-full pl-5 pr-10 py-3.5 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent appearance-none text-gray-500">
                    <option value="" disabled selected>Select Service</option>
                    <option value="kadere">KadereConnect</option>
                    <option value="custom">Custom Development</option>
                    <option value="digital">Digital Transformation</option>
                    <option value="consulting">Business Consulting</option>
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-gray-400">
                    <svg className="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none text-orange-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <input 
                      type="text" 
                      placeholder="mm/dd/yy"
                      className="w-full pl-14 pr-4 py-3.5 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-5 flex items-center pointer-events-none text-orange-500">
                       <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <input 
                      type="text" 
                      placeholder="--:--" 
                      className="w-full pl-14 pr-14 py-3.5 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent"
                    />
                     <div className="absolute inset-y-0 right-0 pr-5 flex items-center pointer-events-none text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.414-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
                
                <div>
                  <textarea 
                    placeholder="Write Message" 
                    rows={4} 
                    className="w-full px-5 py-3.5 rounded-2xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent resize-none"
                  ></textarea>
                </div>
                
                <div className="flex items-center">
                  <input type="checkbox" id="terms" className="h-4 w-4 text-orange-500 border-gray-300 rounded focus:ring-orange-500" />
                  <label htmlFor="terms" className="ml-3 text-gray-600 text-sm">
                    I agree to all terms and conditions.
                  </label>
                </div>
                
                <button 
                  type="submit" 
                  className="w-full bg-teal-900 hover:bg-teal-800 text-white py-4 px-8 rounded-lg font-semibold text-lg flex items-center justify-center transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                >
                  Make an Appointment <span className="ml-2 font-bold text-xl">→</span>
                </button>
              </form>
            </div>
            
            {/* Right Side - Consultation */}
            <div className="flex flex-col justify-start h-full pt-10">
              <div className="mb-8">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center mr-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-orange-500 font-bold uppercase tracking-wider">FREE CONSULTATION</span>
                </div>
                
                <h2 className="text-5xl font-bold text-teal-900 mb-6 leading-tight">
                  Have any talks let's get <br />
                  free <span className="font-extrabold text-teal-800">consultation</span>
                </h2>
              </div>
              
              <div className="relative mt-auto">
                <div className="rounded-2xl overflow-hidden shadow-2xl">
                    <img 
                      src="/team-meeting.jpg" 
                      alt="Consultation meeting" 
                      className="w-full h-[380px] object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-teal-800/80 via-teal-800/40 to-transparent"></div>
                    <div className="absolute bottom-8 left-8 flex items-center">
                        <div className="bg-white/20 backdrop-blur-sm p-4 rounded-full mr-4 border-2 border-white/30">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.518.759a11.03 11.03 0 004.28 4.28l.759-1.518a1 1 0 011.06-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                            </svg>
                        </div>
                        <span className="text-white text-2xl font-bold">(+254)702 236 510</span>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-teal-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Trinova Technologies</h3>
              <p className="text-teal-100/80 mb-4">
                Transforming businesses through innovative technology solutions and strategic consulting.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-teal-100/70 hover:text-white transition-colors">FB.</a>
                <a href="#" className="text-teal-100/70 hover:text-white transition-colors">TW.</a>
                <a href="#" className="text-teal-100/70 hover:text-white transition-colors">LN.</a>
                <a href="#" className="text-teal-100/70 hover:text-white transition-colors">IG</a>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-teal-100/70">
                <li><a href="#" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Our Services</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Meet Our Team</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Services</h4>
              <ul className="space-y-2 text-teal-100/70">
                <li><a href="#" className="hover:text-white transition-colors">Business Consulting</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Digital Transformation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Technology Strategy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Innovation Consulting</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Newsletter</h4>
              <p className="text-teal-100/70 mb-4">Don't miss the latest updates</p>
              <div className="flex">
                <input 
                  type="email" 
                  placeholder="<EMAIL>" 
                  className="flex-1 px-4 py-2 rounded-l-full text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500"
                />
                <button className="bg-orange-500 px-6 py-2 rounded-r-full hover:bg-orange-600 transition-colors">
                  →
                </button>
              </div>
            </div>
          </div>
          <div className="border-t border-teal-800 mt-12 pt-8 text-center text-teal-100/70">
            <p>©2024 - All Rights Reserved by Trinova Technologies</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
