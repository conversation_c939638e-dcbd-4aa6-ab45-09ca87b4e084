# Trinova Website

This repository contains the source code for the Trinova Technologies website.

## Getting Started

1. **Clone the repository** (if you haven't already):

    ```bash
    git clone https://github.com/KadereConnect/Trinova-Technologies-Website

    or

    <NAME_EMAIL>:KadereConnect/Trinova-Technologies-Website.git
    ```

2. **Change into the project directory:**

    ```bash
    cd Trinova-Technologies-Website/trinova-technologies-website
    ```

3. **Install dependencies** (if required):

    ```bash
    npm install
    ```

4. **Run the development server:**

    ```bash
    npm run dev
    ```

The website should now be running locally. Follow any additional instructions in the project documentation as needed.